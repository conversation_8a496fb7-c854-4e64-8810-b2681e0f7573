/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.CORE_TRANSFERS_TDS_ENABLED;
import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.ORCHESTRATOR_ENABLED;
import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.client.CoreTransfersClient;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames;
import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagVariants;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferResponse;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TransferResponseItem;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.web.context.request.RequestContextListener;

@SpringBootTest
@AutoConfigureMockMvc
public class MmCoreTransfersIntegrationTest {

  @Autowired private MockMvc mockMvc;

  @MockBean FeatureFlagService featureFlagService;
  @MockBean CoreTransfersClient coreTransfersClient;
  @MockBean CesClient cesClient;

  // Move RequestContextListener bean to a @TestConfiguration inner class
  @org.springframework.boot.test.context.TestConfiguration
  static class TestConfig {
    @org.springframework.context.annotation.Bean
    public RequestContextListener requestContextListener() {
      return new RequestContextListener();
    }
  }

  static final String BEARER_TOKEN =
      "Bearer ************************************************************************************************************************************************************************************************************************************************************************************************************************************";

  private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

  private String todayDueDate() {
    return LocalDate.now().format(DATE_FORMATTER);
  }

  @Test
  void createCoreTransferHappyPath() throws Exception {
    // Given
    String dueDate = todayDueDate();
    var transferRequest =
        String.format(
            """
            {
                \"requestGuid\": \"test-core-transfer-001\",
                \"fromAccountId\": \"********-A64A-3BCD-8E1A-88785C9CDA12\",
                \"toAccountId\": \"D55D0F37-3BC5-F313-1ED5-B3F86124C896\",
                \"amount\": 100.50,
                \"dueDate\": \"%s\",
                \"frequency\": \"ONE_TIME\",
                \"activityType\": \"INTERNAL_TRANSFER\",
                \"fromAccountType\": \"DDA\",
                \"toAccountType\": \"SAV\"
            }
            """,
            dueDate);

    TDSCoreTransferResponse mockCoreTransferResponse = createMockCoreTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(mockCoreTransferResponse);
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.TRANSFERS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.ENABLED);

    // When & Then
    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("iss", "https://auth.example.com")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferRequest)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("SUCCESS"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.activity.amount").value(100.50))
        .andExpect(MockMvcResultMatchers.jsonPath("$.activity.displayStatus").value("Processed"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.activity.type").value("INTERNAL_TRANSFER"));

    verify(coreTransfersClient, times(1)).tdsCoreTransfer(any());
  }

  @Test
  void getCombinedActivitiesHappyPath() throws Exception {
    // Given - Both CES and Core Transfers succeed
    ClientActivityResponse mockCesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    TDSCoreTransferActivityResponse mockCoreTransferActivityResponse =
        createMockCoreTransferActivityResponse();

    when(cesClient.getTransferAndPayActivity()).thenReturn(mockCesResponse);
    when(coreTransfersClient.getTransferActivity()).thenReturn(mockCoreTransferActivityResponse);
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);

    // When & Then
    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .param("recentLimit", "10")
                .param("upcomingLimit", "10")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("SUCCESS"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities").isArray())
        .andExpect(MockMvcResultMatchers.jsonPath("$.upcomingActivities").isArray());

    verify(cesClient, times(1)).getTransferAndPayActivity();
    verify(coreTransfersClient, times(1)).getTransferActivity();
  }

  @Test
  void getCombinedActivitiesWhenCoreTransfersFailsButCesSucceeds() throws Exception {
    // Given - CES succeeds, Core Transfers fails
    ClientActivityResponse mockCesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);

    when(cesClient.getTransferAndPayActivity()).thenReturn(mockCesResponse);
    when(coreTransfersClient.getTransferActivity())
        .thenThrow(new RuntimeException("Core Transfers service unavailable"));
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);

    // When & Then
    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .param("recentLimit", "10")
                .param("upcomingLimit", "10")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("PARTIAL_SUCCESS"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.retrievalErrors").isArray())
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.retrievalErrors[0]")
                .value("RETRIEVAL_ERROR_CORE_TRANSFERS"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities").isArray())
        .andExpect(MockMvcResultMatchers.jsonPath("$.upcomingActivities").isArray());

    verify(cesClient, times(1)).getTransferAndPayActivity();
    verify(coreTransfersClient, times(1)).getTransferActivity();
  }

  @Test
  void getCombinedActivitiesWhenCesFailsButCoreTransfersSucceeds() throws Exception {
    // Given - CES fails, Core Transfers succeeds
    TDSCoreTransferActivityResponse mockCoreTransferActivityResponse =
        createMockCoreTransferActivityResponse();

    when(cesClient.getTransferAndPayActivity())
        .thenThrow(
            new CesFeignException(HttpStatus.SERVICE_UNAVAILABLE, "CES service unavailable"));
    when(coreTransfersClient.getTransferActivity()).thenReturn(mockCoreTransferActivityResponse);
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);

    // When & Then
    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .param("recentLimit", "10")
                .param("upcomingLimit", "10")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("PARTIAL_SUCCESS"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.retrievalErrors").isArray())
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.retrievalErrors[0]").value("UNABLE_TO_GET_ACTIVITY"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities").isArray())
        .andExpect(MockMvcResultMatchers.jsonPath("$.upcomingActivities").isArray());

    verify(cesClient, times(1)).getTransferAndPayActivity();
    verify(coreTransfersClient, times(1)).getTransferActivity();
  }

  @Test
  void getCombinedActivitiesWhenBothServicesFailShouldReturnServiceUnavailable() throws Exception {
    // Given - Both CES and Core Transfers fail
    when(cesClient.getTransferAndPayActivity())
        .thenThrow(
            new CesFeignException(HttpStatus.SERVICE_UNAVAILABLE, "CES service unavailable"));
    when(coreTransfersClient.getTransferActivity())
        .thenThrow(new RuntimeException("Core Transfers service unavailable"));
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);

    // When & Then
    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .param("recentLimit", "10")
                .param("upcomingLimit", "10")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable());

    verify(cesClient, times(1)).getTransferAndPayActivity();
    verify(coreTransfersClient, times(1)).getTransferActivity();
  }

  @Test
  void createCoreTransferAndVerifyInActivityList() throws Exception {
    // Step 1: Create a core transfer
    String dueDate = todayDueDate();
    var transferRequest =
        String.format(
            """
            {
                \"requestGuid\": \"test-core-transfer-e2e-001\",
                \"fromAccountId\": \"********-A64A-3BCD-8E1A-88785C9CDA12\",
                \"toAccountId\": \"D55D0F37-3BC5-F313-1ED5-B3F86124C896\",
                \"amount\": 250.75,
                \"dueDate\": \"%s\",
                \"frequency\": \"ONE_TIME\",
                \"activityType\": \"INTERNAL_TRANSFER\",
                \"fromAccountType\": \"DDA\",
                \"toAccountType\": \"SAV\"
            }
            """,
            dueDate);

    TDSCoreTransferResponse mockCoreTransferResponse = createMockCoreTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(mockCoreTransferResponse);
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);

    // Create the transfer
    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferRequest)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("SUCCESS"));

    // Step 2: Verify the transfer appears in the activity list
    ClientActivityResponse mockCesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    TDSCoreTransferActivityResponse mockCoreTransferActivityResponse =
        createMockCoreTransferActivityResponseWithCreatedTransfer();

    when(cesClient.getTransferAndPayActivity()).thenReturn(mockCesResponse);
    when(coreTransfersClient.getTransferActivity()).thenReturn(mockCoreTransferActivityResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .param("recentLimit", "10")
                .param("upcomingLimit", "10")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("SUCCESS"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities").isArray())
        .andExpect(MockMvcResultMatchers.jsonPath("$.upcomingActivities").isArray());

    verify(coreTransfersClient, times(1)).tdsCoreTransfer(any());
    verify(cesClient, times(1)).getTransferAndPayActivity();
    verify(coreTransfersClient, times(1)).getTransferActivity();
  }

  @Test
  void getCombinedActivitiesWithLimitsAppliedCorrectly() throws Exception {
    // Given - Both services return data, test that limits are applied correctly
    ClientActivityResponse mockCesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    TDSCoreTransferActivityResponse mockCoreTransferActivityResponse =
        createMockCoreTransferActivityResponse();

    when(cesClient.getTransferAndPayActivity()).thenReturn(mockCesResponse);
    when(coreTransfersClient.getTransferActivity()).thenReturn(mockCoreTransferActivityResponse);
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);

    // When & Then - Test with small limits
    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .param("recentLimit", "2")
                .param("upcomingLimit", "3")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("SUCCESS"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities").isArray())
        .andExpect(MockMvcResultMatchers.jsonPath("$.upcomingActivities").isArray());

    verify(cesClient, times(1)).getTransferAndPayActivity();
    verify(coreTransfersClient, times(1)).getTransferActivity();
  }

  // Helper methods for creating mock responses

  private TDSCoreTransferResponse createMockCoreTransferResponse() {
    TDSCoreTransferResponse response = new TDSCoreTransferResponse();
    TransferResponseItem transfer = new TransferResponseItem();
    transfer.setId("CT-12345");
    transfer.setAmount(BigDecimal.valueOf(100.50));
    transfer.setCreatedDate(LocalDate.now().format(DATE_FORMATTER));
    List<TransferResponseItem> transfers = new ArrayList<>();
    transfers.add(transfer);
    response.setTransfers(transfers);
    return response;
  }

  private TDSCoreTransferActivityResponse createMockCoreTransferActivityResponse() {
    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    List<TDSCoreTransferActivity> activities = new ArrayList<>();
    LocalDate today = LocalDate.now();
    // Create a completed transfer activity
    TDSCoreTransferActivity completedActivity = new TDSCoreTransferActivity();
    completedActivity.setReferenceId("CT-COMPLETED-001");
    completedActivity.setFromAccountId("********-A64A-3BCD-8E1A-88785C9CDA12");
    completedActivity.setToAccountId("D55D0F37-3BC5-F313-1ED5-B3F86124C896");
    completedActivity.setToAccountType("SAV");
    completedActivity.setAmount(BigDecimal.valueOf(50.00));
    completedActivity.setCreatedDate(today);
    completedActivity.setExpectedPostingDate(today); // Same date = completed
    completedActivity.setTransferStatus("SUCCESS");
    activities.add(completedActivity);
    // Create a scheduled transfer activity
    TDSCoreTransferActivity scheduledActivity = new TDSCoreTransferActivity();
    scheduledActivity.setReferenceId("CT-SCHEDULED-001");
    scheduledActivity.setFromAccountId("********-A64A-3BCD-8E1A-88785C9CDA12");
    scheduledActivity.setToAccountId("D55D0F37-3BC5-F313-1ED5-B3F86124C896");
    scheduledActivity.setToAccountType("SAV");
    scheduledActivity.setAmount(BigDecimal.valueOf(75.25));
    scheduledActivity.setCreatedDate(today);
    scheduledActivity.setExpectedPostingDate(today.plusDays(5)); // scheduled for 5 days later
    scheduledActivity.setTransferStatus("SUCCESS");
    activities.add(scheduledActivity);
    response.setTransferActivities(activities);
    return response;
  }

  private TDSCoreTransferActivityResponse
      createMockCoreTransferActivityResponseWithCreatedTransfer() {
    TDSCoreTransferActivityResponse response = createMockCoreTransferActivityResponse();
    LocalDate today = LocalDate.now();
    TDSCoreTransferActivity createdActivity = new TDSCoreTransferActivity();
    createdActivity.setReferenceId("test-core-transfer-e2e-001");
    createdActivity.setFromAccountId("********-A64A-3BCD-8E1A-88785C9CDA12");
    createdActivity.setToAccountId("D55D0F37-3BC5-F313-1ED5-B3F86124C896");
    createdActivity.setToAccountType("SAV");
    createdActivity.setAmount(BigDecimal.valueOf(250.75));
    createdActivity.setCreatedDate(today);
    createdActivity.setExpectedPostingDate(today); // Same date = completed
    createdActivity.setTransferStatus("SUCCESS");

    response.getTransferActivities().add(createdActivity);
    return response;
  }
}
